# Here is the doc for the process and reference website information of developing committee and PPMC members.


## 1. How to develop committers

### Preconditions

1. More than 10 commits from the contributor
2. Contributor is willing to accept the invitation to become a committer
3. Contributor <NAME_EMAIL> and introduce himself by sending an <NAME_EMAIL>

### The journey to become a committer

1. The nominator sends an email to private@brpc to initiate discussion and begins to voting. If the voting is passed, it is OK (at least 3 +1, +1 >- 1). (See the [Vote email template](https://community.apache.org/newcommitter.html#committer-vote-template))
2. The nominator sends a close vote email to private@brpc, the title can be subject [RESULT] [VOTE]. (See the [close email template](https://community.apache.org/newcommitter.html#close-vote))
3. The nominator sends an invitation letter to the nominee ([email template](https://community.apache.org/newcommitter.html#committer-invite-template)) and prompts him to submit ICLA after receiving a reply ([email template](https://community.apache.org/newcommitter.html#committer-accept-template))
4. The nominee fills in [ICLA](https://www.apache.org/licenses/contributor-agreements.html), individual contributors need to download [ICLA](https://www.apache.org/licenses/icla.pdf) Fill in personal information and sign, and send the electronic <NAME_EMAIL> 。 (Note: 1. ICLA needs to fill in complete information, including mailing address and signature, otherwise it will be returned by ASF's secretary. 2. ICLA contains personal information, it should be <NAME_EMAIL> only, not to the PMC or other mail list) The personal information entry (except for the signature) can be filled in with a PDF reader or browser, and then saved for signature. Signature method support:
   * Print pdf documents and scan them into electronic version after handwritten signature;
   * Electronic signature using devices that support handwriting;
   * Use 'gpg' for electronic signature, that is, to operate the pdf file filled with personal basic information (a public key/key pair matching the registered mailbox needs to be generated in advance): 'gpg -- armor -- detach sign icla. pdf';
   * Use 'DocuSign' to sign;
5. The nominator sends an announcement <NAME_EMAIL>

### How to grant a committer permission on github

1. Add as committer ([https://whimsy.apache.org/roster/ppmc/brpc](https://whimsy.apache.org/roster/ppmc/brpc))
2. Let him set github id  ([https://id.apache.org/](https://id.apache.org/))
3. Let him visit the website and get github permission([https://gitbox.apache.org/setup/](https://id.apache.org/))

### Documents related to the new committer on the official Apache website

* [https://community.apache.org/newcommitter.html](https://community.apache.org/newcommitter.html)
* [https://infra.apache.org/new-committers-guide.html](https://infra.apache.org/new-committers-guide.html)
* [https://juejin.cn/post/6844903788982042632](https://juejin.cn/post/6844903788982042632)

### Suggested <NAME_EMAIL>

Please do these things:
1. Hold the discussion and vote on your private@ list. This avoids any issues related to personnel, which should remain private.
2. If the vote is successful, announce the result to the private@ list with a new email thread with subject [RESULT][VOTE]. This makes it easier for secretary to find the result of the vote in order to request the account at the time of the filing of the ICLA.
3. Only if the candidate accepts committership, announce the new committer on your dev@ list.

Doing these things will make everyone's job easier.

## 2. How to upgrade a committer into a PPMC

### Process reference: Apache official website document

* https://incubator.apache.org/guides/ppmc.html#voting_in_a_new_ppmc_member
* https://community.apache.org/newcommitter.html
* https://incubator.apache.org/guides/ppmc.html#podling_project_management_committee_ppmc

### Actual process

1. Initiate discussion in the private@brpc . If there is no objection, continue
2. Open a Vote in private@brpc 
3. Send an email to close the voting <NAME_EMAIL>
4. Announce new PPMC On private@brpc 
5. Set his authority by visiting https://whimsy.apache.org/roster/ppmc/brpc
6. Help him subscribe to a private email group. See https://whimsy.apache.org/committers/moderationhelper.cgi
