<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1059px" height="779px" viewBox="-0.5 -0.5 1059 779" style="background-color: rgb(255, 255, 255);"><defs/><rect fill="#ffffff" width="100%" height="100%" x="0" y="0"/><g><g data-cell-id="g0N0ArMlp0iuiEGkKfUx-0"><g data-cell-id="g0N0ArMlp0iuiEGkKfUx-1"><g data-cell-id="g0N0ArMlp0iuiEGkKfUx-35"><g><rect x="168" y="113" width="150" height="60" rx="4.2" ry="4.2" fill="#e85642" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 142px; height: 1px; padding-top: 143px; margin-left: 172px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">创建</div></div></div></foreignObject><text x="243" y="149" fill="#FFFFFF" font-family="&quot;Helvetica&quot;" font-size="20px" text-anchor="middle" font-weight="bold">创建</text></switch></g></g></g><g data-cell-id="g0N0ArMlp0iuiEGkKfUx-36"><g><path d="M 398 108 L 808 108 L 828 128 L 828 178 L 418 178 L 398 158 L 398 108 Z" fill="#64bbe2" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 398 108 L 808 108 L 828 128 L 418 128 Z" fill-opacity="0.05" fill="#000000" stroke="none" pointer-events="all"/><path d="M 398 108 L 418 128 L 418 178 L 398 158 Z" fill-opacity="0.1" fill="#000000" stroke="none" pointer-events="all"/><path d="M 418 178 L 418 128 L 398 108 M 418 128 L 828 128" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 408px; height: 1px; padding-top: 153px; margin-left: 419px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font color="#ffffff" style="font-size: 20px;">就绪（调度队列）</font></div></div></div></foreignObject><text x="623" y="159" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="20px" text-anchor="middle">就绪（调度队列）</text></switch></g></g></g><g data-cell-id="g0N0ArMlp0iuiEGkKfUx-37"><g><rect x="168" y="318" width="150" height="60" rx="4.2" ry="4.2" fill="#61c6ce" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 142px; height: 1px; padding-top: 348px; margin-left: 172px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 17px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">将运行（<span style="color: rgb(247, 92, 70); font-size: 17px;">拦截点</span>）</div></div></div></foreignObject><text x="243" y="353" fill="#FFFFFF" font-family="&quot;Helvetica&quot;" font-size="17px" text-anchor="middle">将运行（拦截点）</text></switch></g></g></g><g data-cell-id="g0N0ArMlp0iuiEGkKfUx-39"><g><rect x="168" y="528" width="150" height="60" rx="4.2" ry="4.2" fill="#12aab5" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 142px; height: 1px; padding-top: 558px; margin-left: 172px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">运行</div></div></div></foreignObject><text x="243" y="564" fill="#FFFFFF" font-family="&quot;Helvetica&quot;" font-size="20px" text-anchor="middle" font-weight="bold">运行</text></switch></g></g></g><g data-cell-id="g0N0ArMlp0iuiEGkKfUx-40"><g><rect x="908" y="318" width="150" height="60" rx="4.2" ry="4.2" fill="#f08705" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 142px; height: 1px; padding-top: 348px; margin-left: 912px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">挂起</div></div></div></foreignObject><text x="983" y="354" fill="#FFFFFF" font-family="&quot;Helvetica&quot;" font-size="20px" text-anchor="middle" font-weight="bold">挂起</text></switch></g></g></g><g data-cell-id="g0N0ArMlp0iuiEGkKfUx-41"><g><rect x="908" y="528" width="150" height="60" rx="4.2" ry="4.2" fill="#f5af58" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 142px; height: 1px; padding-top: 558px; margin-left: 912px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 17px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">挂起中（<span style="color: rgb(247, 92, 70); font-size: 17px;">拦截点</span>）</div></div></div></foreignObject><text x="983" y="563" fill="#FFFFFF" font-family="&quot;Helvetica&quot;" font-size="17px" text-anchor="middle">挂起中（拦截点）</text></switch></g></g></g><g data-cell-id="g0N0ArMlp0iuiEGkKfUx-42"><g><rect x="168" y="718" width="150" height="60" rx="4.2" ry="4.2" fill="#f08e81" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 142px; height: 1px; padding-top: 748px; margin-left: 172px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">销毁</div></div></div></foreignObject><text x="243" y="754" fill="#FFFFFF" font-family="&quot;Helvetica&quot;" font-size="20px" text-anchor="middle">销毁</text></switch></g></g></g><g data-cell-id="g0N0ArMlp0iuiEGkKfUx-43"><g><rect x="428" y="343" width="60" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 358px; margin-left: 429px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">sched<span style="background-color: initial; font-size: 20px;">_to</span></div></div></div></foreignObject><text x="458" y="364" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="20px" text-anchor="middle">sched_...</text></switch></g></g></g><g data-cell-id="g0N0ArMlp0iuiEGkKfUx-44"><g><rect x="598" y="228" width="310" height="50" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 308px; height: 1px; padding-top: 253px; margin-left: 599px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">yield/wakeup/timeout/interrupt</div></div></div></foreignObject><text x="753" y="259" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="20px" text-anchor="middle">yield/wakeup/timeout/interrupt</text></switch></g></g></g><g data-cell-id="g0N0ArMlp0iuiEGkKfUx-45"><g><rect x="668" y="498" width="130" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 128px; height: 1px; padding-top: 518px; margin-left: 669px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">sched<span style="background-color: initial; font-size: 20px;">_to</span></div></div></div></foreignObject><text x="733" y="524" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="20px" text-anchor="middle">sched_to</text></switch></g></g></g><g data-cell-id="g0N0ArMlp0iuiEGkKfUx-48"><g><path d="M 418 178 L 261.32 311.18" fill="none" stroke="#2f5b7c" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 256.18 315.56 L 260.12 306.3 L 261.32 311.18 L 265.95 313.16 Z" fill="#2f5b7c" stroke="#2f5b7c" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="g0N0ArMlp0iuiEGkKfUx-49"><g><path d="M 318 348 L 530.89 410.84" fill="none" stroke="#2f5b7c" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 537.36 412.75 L 527.46 414.52 L 530.89 410.84 L 530.01 405.89 Z" fill="#2f5b7c" stroke="#2f5b7c" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="g0N0ArMlp0iuiEGkKfUx-50"><g><path d="M 557.94 468 L 329.82 523.61" fill="none" stroke="#2f5b7c" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 323.26 525.21 L 330.94 518.7 L 329.82 523.61 L 333.07 527.45 Z" fill="#2f5b7c" stroke="#2f5b7c" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="g0N0ArMlp0iuiEGkKfUx-51"><g><path d="M 320 574 L 895.9 574" fill="none" stroke="#2f5b7c" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 902.65 574 L 893.65 578.5 L 895.9 574 L 893.65 569.5 Z" fill="#2f5b7c" stroke="#2f5b7c" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="g0N0ArMlp0iuiEGkKfUx-52"><g><path d="M 906 526 L 676.99 475.64" fill="none" stroke="#2f5b7c" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 670.4 474.19 L 680.16 471.73 L 676.99 475.64 L 678.22 480.52 Z" fill="#2f5b7c" stroke="#2f5b7c" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="g0N0ArMlp0iuiEGkKfUx-53"><g><path d="M 684.67 419.77 L 896.2 366.47" fill="none" stroke="#2f5b7c" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 902.75 364.82 L 895.12 371.38 L 896.2 366.47 L 892.92 362.66 Z" fill="#2f5b7c" stroke="#2f5b7c" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="g0N0ArMlp0iuiEGkKfUx-54"><g><path d="M 906 332 L 622.42 386.11" fill="none" stroke="#2f5b7c" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 615.79 387.37 L 623.79 381.26 L 622.42 386.11 L 625.48 390.1 Z" fill="#2f5b7c" stroke="#2f5b7c" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="g0N0ArMlp0iuiEGkKfUx-55"><g><path d="M 320 143 L 377.9 143" fill="none" stroke="#2f5b7c" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 384.65 143 L 375.65 147.5 L 377.9 143 L 375.65 138.5 Z" fill="#2f5b7c" stroke="#2f5b7c" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="g0N0ArMlp0iuiEGkKfUx-56"><g><path d="M 243 590 L 243 705.9" fill="none" stroke="#2f5b7c" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 243 712.65 L 238.5 703.65 L 243 705.9 L 247.5 703.65 Z" fill="#2f5b7c" stroke="#2f5b7c" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="g0N0ArMlp0iuiEGkKfUx-57"><g><rect x="148" y="198" width="60" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 213px; margin-left: 149px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">start_urgent</div></div></div></foreignObject><text x="178" y="219" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="20px" text-anchor="middle">start_...</text></switch></g></g></g><g data-cell-id="g0N0ArMlp0iuiEGkKfUx-60"><g><path d="M 612.5 388 L 611.75 190.41" fill="none" stroke="#2f5b7c" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 611.72 183.66 L 616.26 192.65 L 611.75 190.41 L 607.26 192.68 Z" fill="#2f5b7c" stroke="#2f5b7c" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="g0N0ArMlp0iuiEGkKfUx-61"><g><rect x="708" y="419" width="150" height="20" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 148px; height: 1px; padding-top: 429px; margin-left: 709px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">butex/sleep</div></div></div></foreignObject><text x="783" y="435" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="20px" text-anchor="middle">butex/sleep</text></switch></g></g></g><g data-cell-id="g0N0ArMlp0iuiEGkKfUx-62"><g><path d="M 242.29 171 L 242.95 305.9" fill="none" stroke="#2f5b7c" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 242.98 312.65 L 238.44 303.67 L 242.95 305.9 L 247.44 303.62 Z" fill="#2f5b7c" stroke="#2f5b7c" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="C6VWxwcYETg3vNQvPhRN-0"><g><rect x="328" y="68" width="60" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 83px; margin-left: 329px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">start_background/start_urgent</div></div></div></foreignObject><text x="358" y="89" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="20px" text-anchor="middle">start_...</text></switch></g></g></g><g data-cell-id="354THMuuuv9kz7hq0Oxp-3"><g><ellipse cx="612.5" cy="438" rx="77.5" ry="50" fill="#18a8e8" stroke="rgb(0, 0, 0)" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 153px; height: 1px; padding-top: 438px; margin-left: 536px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><span style="color: rgb(255, 255, 255); font-size: 20px; font-weight: 700;">jump_stack</span></div></div></div></foreignObject><text x="613" y="442" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">jump_stack</text></switch></g></g></g><g data-cell-id="xq_Ipxr0y4D4y-EKlOhB-0"><g><rect x="678" y="328" width="60" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 343px; margin-left: 679px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">sched<span style="background-color: initial; font-size: 20px;">_to</span></div></div></div></foreignObject><text x="708" y="349" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="20px" text-anchor="middle">sched_...</text></switch></g></g></g><g data-cell-id="l02yRmDLPTxZdvzgLWH2-0"><g><rect x="358" y="8" width="230" height="50" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 228px; height: 1px; padding-top: 33px; margin-left: 359px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">sched<span style="background-color: initial;">_to</span>(ending_sched)</div></div></div></foreignObject><text x="473" y="39" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="20px" text-anchor="middle">sched_to(ending_sched)</text></switch></g></g></g><g data-cell-id="9m2d9J1Dqto998j0rekb-0"><g><path d="M 598 8 L 8 8 L 8 558 L 157.9 558" fill="none" stroke="#2f5b7c" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 164.65 558 L 155.65 562.5 L 157.9 558 L 155.65 553.5 Z" fill="#2f5b7c" stroke="#2f5b7c" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="h_SlPDvQ2YQu-YS3PsDQ-0"><g><path d="M 598 105 L 598 8" fill="none" stroke="#2f5b7c" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/></g></g></g></g></g></svg>