<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1045px" height="669px" viewBox="-0.5 -0.5 1045 669" style="background-color: rgb(255, 255, 255);"><defs/><rect fill="#ffffff" width="100%" height="100%" x="0" y="0"/><g><g data-cell-id="0"><g data-cell-id="1"><g data-cell-id="DboKUKvH6xi3xOyzVJv4-1887"><g><rect x="151" y="153" width="150" height="60" rx="4.2" ry="4.2" fill="#e85642" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 142px; height: 1px; padding-top: 183px; margin-left: 155px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">创建</div></div></div></foreignObject><text x="226" y="189" fill="#FFFFFF" font-family="&quot;Helvetica&quot;" font-size="20px" text-anchor="middle" font-weight="bold">创建</text></switch></g></g></g><g data-cell-id="DboKUKvH6xi3xOyzVJv4-1888"><g><path d="M 381 148 L 791 148 L 811 168 L 811 218 L 401 218 L 381 198 L 381 148 Z" fill="#64bbe2" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 381 148 L 791 148 L 811 168 L 401 168 Z" fill-opacity="0.05" fill="#000000" stroke="none" pointer-events="all"/><path d="M 381 148 L 401 168 L 401 218 L 381 198 Z" fill-opacity="0.1" fill="#000000" stroke="none" pointer-events="all"/><path d="M 401 218 L 401 168 L 381 148 M 401 168 L 811 168" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 408px; height: 1px; padding-top: 193px; margin-left: 402px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font color="#ffffff" style="font-size: 20px;">就绪（调度队列）</font></div></div></div></foreignObject><text x="606" y="199" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="20px" text-anchor="middle">就绪（调度队列）</text></switch></g></g></g><g data-cell-id="DboKUKvH6xi3xOyzVJv4-1891"><g><rect x="151" y="459" width="150" height="60" rx="4.2" ry="4.2" fill="#12aab5" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 142px; height: 1px; padding-top: 489px; margin-left: 155px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">运行中</div></div></div></foreignObject><text x="226" y="495" fill="#FFFFFF" font-family="&quot;Helvetica&quot;" font-size="20px" text-anchor="middle" font-weight="bold">运行中</text></switch></g></g></g><g data-cell-id="DboKUKvH6xi3xOyzVJv4-1892"><g><rect x="893.5" y="459" width="150" height="60" rx="4.2" ry="4.2" fill="#f08705" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 142px; height: 1px; padding-top: 489px; margin-left: 898px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">挂起</div></div></div></foreignObject><text x="969" y="495" fill="#FFFFFF" font-family="&quot;Helvetica&quot;" font-size="20px" text-anchor="middle" font-weight="bold">挂起</text></switch></g></g></g><g data-cell-id="DboKUKvH6xi3xOyzVJv4-1894"><g><rect x="151" y="608" width="150" height="60" rx="4.2" ry="4.2" fill="#f08e81" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 142px; height: 1px; padding-top: 638px; margin-left: 155px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">销毁</div></div></div></foreignObject><text x="226" y="644" fill="#FFFFFF" font-family="&quot;Helvetica&quot;" font-size="20px" text-anchor="middle">销毁</text></switch></g></g></g><g data-cell-id="DboKUKvH6xi3xOyzVJv4-1895"><g><rect x="511" y="258" width="60" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 273px; margin-left: 512px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">sched<span style="background-color: initial; font-size: 20px;">_to</span></div></div></div></foreignObject><text x="541" y="279" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="20px" text-anchor="middle">sched_...</text></switch></g></g></g><g data-cell-id="DboKUKvH6xi3xOyzVJv4-1896"><g><rect x="611" y="374" width="290" height="52" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 288px; height: 1px; padding-top: 400px; margin-left: 612px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">yield/wakeup/timeout/interrupt</div></div></div></foreignObject><text x="756" y="406" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="20px" text-anchor="middle">yield/wakeup/timeout/interrupt</text></switch></g></g></g><g data-cell-id="DboKUKvH6xi3xOyzVJv4-1897"><g><rect x="251" y="498" width="270" height="60" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 268px; height: 1px; padding-top: 528px; margin-left: 252px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">sched<span style="background-color: initial; font-size: 20px;">_to</span></div></div></div></foreignObject><text x="386" y="534" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="20px" text-anchor="middle">sched_to</text></switch></g></g></g><g data-cell-id="DboKUKvH6xi3xOyzVJv4-1900"><g><path d="M 441 217 L 602.41 430.93" fill="none" stroke="#2f5b7c" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 606.48 436.32 L 597.47 431.85 L 602.41 430.93 L 604.65 426.43 Z" fill="#2f5b7c" stroke="#2f5b7c" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="DboKUKvH6xi3xOyzVJv4-1902"><g><path d="M 531 473 L 313.1 473" fill="none" stroke="#2f5b7c" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 306.35 473 L 315.35 468.5 L 313.1 473 L 315.35 477.5 Z" fill="#2f5b7c" stroke="#2f5b7c" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="DboKUKvH6xi3xOyzVJv4-1903"><g><path d="M 303 505 L 520.59 504.71" fill="none" stroke="#2f5b7c" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 527.34 504.7 L 518.34 509.22 L 520.59 504.71 L 518.33 500.22 Z" fill="#2f5b7c" stroke="#2f5b7c" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="DboKUKvH6xi3xOyzVJv4-1905"><g><path d="M 686 508.55 L 880.9 508.98" fill="none" stroke="#2f5b7c" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 887.65 508.99 L 878.64 513.47 L 880.9 508.98 L 878.66 504.47 Z" fill="#2f5b7c" stroke="#2f5b7c" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="DboKUKvH6xi3xOyzVJv4-1906"><g><path d="M 891.5 473 L 701.1 473" fill="none" stroke="#2f5b7c" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 694.35 473 L 703.35 468.5 L 701.1 473 L 703.35 477.5 Z" fill="#2f5b7c" stroke="#2f5b7c" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="DboKUKvH6xi3xOyzVJv4-1907"><g><path d="M 303 183 L 360.9 183" fill="none" stroke="#2f5b7c" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 367.65 183 L 358.65 187.5 L 360.9 183 L 358.65 178.5 Z" fill="#2f5b7c" stroke="#2f5b7c" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="DboKUKvH6xi3xOyzVJv4-1908"><g><path d="M 226 521 L 226 595.9" fill="none" stroke="#2f5b7c" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 226 602.65 L 221.5 593.65 L 226 595.9 L 230.5 593.65 Z" fill="#2f5b7c" stroke="#2f5b7c" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="DboKUKvH6xi3xOyzVJv4-1909"><g><rect x="251" y="338" width="230" height="50" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 228px; height: 1px; padding-top: 363px; margin-left: 252px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 19px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">start_urgent(sched<span style="background-color: initial; font-size: 19px;">_to</span>)</div></div></div></foreignObject><text x="366" y="369" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="19px" text-anchor="middle">start_urgent(sched_to)</text></switch></g></g></g><g data-cell-id="DboKUKvH6xi3xOyzVJv4-1911"><g><path d="M 608.5 439 L 608.98 228.1" fill="none" stroke="#2f5b7c" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 608.99 221.35 L 613.47 230.36 L 608.98 228.1 L 604.47 230.34 Z" fill="#2f5b7c" stroke="#2f5b7c" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="DboKUKvH6xi3xOyzVJv4-1912"><g><rect x="711" y="519" width="150" height="20" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 148px; height: 1px; padding-top: 529px; margin-left: 712px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">butex/sleep</div></div></div></foreignObject><text x="786" y="535" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="20px" text-anchor="middle">butex/sleep</text></switch></g></g></g><g data-cell-id="DboKUKvH6xi3xOyzVJv4-1913"><g><path d="M 226 215 L 599.78 433.89" fill="none" stroke="#2f5b7c" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 605.61 437.31 L 595.57 436.64 L 599.78 433.89 L 600.11 428.87 Z" fill="#2f5b7c" stroke="#2f5b7c" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="mtJdYWc7ilgYfUn7YYt0-1"><g><rect x="313.5" y="108" width="60" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 123px; margin-left: 315px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">start_background/start_urgent</div></div></div></foreignObject><text x="344" y="129" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="20px" text-anchor="middle">start_...</text></switch></g></g></g><g data-cell-id="we-x0hO6u5tj5KsSbE_x-1"><g><ellipse cx="608.5" cy="489" rx="77.5" ry="50" fill="#18a8e8" stroke="rgb(0, 0, 0)" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 153px; height: 1px; padding-top: 489px; margin-left: 532px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><span style="color: rgb(255, 255, 255); font-size: 20px; font-weight: 700;">jump_stack</span></div></div></div></foreignObject><text x="609" y="493" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">jump_stack</text></switch></g></g></g><g data-cell-id="THPv8lVUmJCEHmGVxGZr-1"><g><path d="M 588 8 L 8 8 L 8 488 L 137.9 488" fill="none" stroke="#2f5b7c" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 144.65 488 L 135.65 492.5 L 137.9 488 L 135.65 483.5 Z" fill="#2f5b7c" stroke="#2f5b7c" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="4T0WWY2YameiYO5vUkhb-1"><g><rect x="313.5" y="18" width="230" height="50" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 228px; height: 1px; padding-top: 43px; margin-left: 315px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">sched<span style="background-color: initial; font-size: 20px;">_to(</span>ending_sched<span style="background-color: initial; font-size: 20px;">)</span></div></div></div></foreignObject><text x="429" y="49" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="20px" text-anchor="middle">sched_to(ending_sched)</text></switch></g></g></g><g data-cell-id="AGelsPaElFCFLniyRYHn-4"><g><path d="M 586 148 L 586 8" fill="none" stroke="#2f5b7c" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/></g></g></g></g></g></svg>