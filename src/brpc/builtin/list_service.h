// Licensed to the Apache Software Foundation (ASF) under one
// or more contributor license agreements.  See the NOTICE file
// distributed with this work for additional information
// regarding copyright ownership.  The ASF licenses this file
// to you under the Apache License, Version 2.0 (the
// "License"); you may not use this file except in compliance
// with the License.  You may obtain a copy of the License at
//
//   http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing,
// software distributed under the License is distributed on an
// "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
// KIND, either express or implied.  See the License for the
// specific language governing permissions and limitations
// under the License.


#ifndef  BRPC_LIST_SERVICE_H
#define  BRPC_LIST_SERVICE_H

#include <ostream>
#include "brpc/builtin_service.pb.h"

namespace brpc {

class Server;

class ListService : public list {
public:
    explicit ListService(Server* server) : _server(server) {}
    
    void default_method(::google::protobuf::Rpc<PERSON>ontroller* cntl_base,
                        const ::brpc::ListRequest* request,
                        ::brpc::ListResponse* response,
                        ::google::protobuf::Closure* done);
private:
    Server* _server;
};

} // namespace brpc


#endif  //BRPC_LIST_SERVICE_H
