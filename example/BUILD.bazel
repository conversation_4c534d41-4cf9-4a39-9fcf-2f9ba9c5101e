# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

load("@rules_proto//proto:defs.bzl", "proto_library")
load("@rules_cc//cc:defs.bzl", "cc_binary", "cc_proto_library")

COPTS = [
    "-D__STDC_FORMAT_MACROS",
    "-DBTHREAD_USE_FAST_PTHREAD_MUTEX",
    "-D__const__=__unused__",
    "-D_GNU_SOURCE",
    "-DUSE_SYMBOLIZE",
    "-<PERSON>N<PERSON>_TCMALLOC",
    "-D__STDC_LIMIT_MACROS",
    "-D__STDC_CONSTANT_MACROS",
    "-fPIC",
    "-Wno-unused-parameter",
    "-fno-omit-frame-pointer",
] + select({
    "//bazel/config:brpc_with_glog": ["-DBRPC_WITH_GLOG=1"],
    "//conditions:default": ["-DBRPC_WITH_GLOG=0"],
}) + select({
    "//bazel/config:brpc_with_rdma": ["-DBRPC_WITH_RDMA=1"],
    "//conditions:default": [""],
})

proto_library(
    name = "echo_c++_proto",
    srcs = [
        "echo_c++/echo.proto",
    ],
)

proto_library(
    name = "rdma_performance_proto",
    srcs = [
        "rdma_performance/test.proto",
    ],
)

cc_proto_library(
    name = "cc_echo_c++_proto",
    deps = [
        ":echo_c++_proto",
    ],
)

cc_proto_library(
    name = "cc_rdma_performance_proto",
    deps = [
        ":rdma_performance_proto",
    ],
)

cc_binary(
    name = "echo_c++_server",
    srcs = [
        "echo_c++/server.cpp",
    ],
    copts = COPTS,
    includes = [
        "echo_c++",
    ],
    deps = [
        ":cc_echo_c++_proto",
        "//:brpc",
    ],
)

cc_binary(
    name = "echo_c++_client",
    srcs = [
        "echo_c++/client.cpp",
    ],
    copts = COPTS,
    includes = [
        "echo_c++",
    ],
    deps = [
        ":cc_echo_c++_proto",
        "//:brpc",
    ],
)

cc_binary(
    name = "rdma_performance_server",
    srcs = [
        "rdma_performance/server.cpp",
    ],
    includes = [
        "rdma_performance",
    ],
    copts = COPTS + ["-DBRPC_WITH_RDMA=1"],
    deps = [
        ":cc_rdma_performance_proto",
        "//:brpc",
    ],
)

cc_binary(
    name = "rdma_performance_client",
    srcs = [
        "rdma_performance/client.cpp",
    ],
    includes = [
        "rdma_performance",
    ],
    copts = COPTS + ["-DBRPC_WITH_RDMA=1"],
    deps = [
        ":cc_rdma_performance_proto",
        "//:brpc",
    ],
)

cc_binary(
    name = "redis_c++_server",
    srcs = [
        "redis_c++/redis_server.cpp",
    ],
    copts = COPTS,
    deps = [
        "//:brpc",
    ],
)